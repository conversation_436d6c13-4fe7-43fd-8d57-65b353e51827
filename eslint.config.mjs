import antfu from '@antfu/eslint-config';
import oxlint from 'eslint-plugin-oxlint';

export default antfu({
  ignores: ['node_modules/', 'node_modules/**/', '.vscode/', '.vscode/**/', 'public/', 'public/**/', 'dist/', 'dist/**/', 'tsconfig.json', '**/tsconfig.json/**', 'assets/', '**/assets/**'],
  formatters: true,
  unocss: true,
  stylistic: {
    semi: true,
  },
  typescript: true,
  vue: {
    overrides: {
      'vue/block-order': ['error', {
        order: ['template', 'script', 'style'],
      }],
      'vue/custom-event-name-casing': ['warn'],
    },
  },
  rules: {
    'no-console': 'off',
  },
  ...oxlint.configs['flat/recommended'], // oxlint should be the last one
});
