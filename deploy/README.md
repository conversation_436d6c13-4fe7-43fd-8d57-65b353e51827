# H5 Frontend 部署文档

## 项目结构

```
deploy/
├── Dockerfile              # Docker 镜像构建文件
├── docker-compose.yml      # Docker Compose 配置
├── .env.example            # 环境变量示例
├── nginx.conf              # Nginx 配置文件
└── kustomize/              # Kubernetes 配置目录
    ├── base/               # 基础配置
    │   ├── deployment.yml  # Kubernetes 部署配置
    │   ├── ingress.yml     # Kubernetes Ingress 配置
    │   └── kustomization.yml
    └── overlays/           # 环境特定配置
        ├── prod/           # 生产环境
        └── dev/            # 开发环境
```

## Docker Compose 部署

### 1. 准备环境变量

```bash
cd deploy
cp .env.example .env
# 编辑 .env 文件设置环境变量
```

### 2. 构建和启动

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 3. 停止服务

```bash
docker-compose down
```

## Kubernetes 部署

### 使用 Kustomize 部署

#### 生产环境部署

```bash
# 部署到生产环境
kubectl apply -k deploy/kustomize/overlays/prod

# 查看部署状态
kubectl get pods -n h5-prod
kubectl get ingress -n h5-prod
```

#### 开发环境部署

```bash
# 部署到开发环境
kubectl apply -k deploy/kustomize/overlays/dev

# 查看部署状态
kubectl get pods -n h5-dev
kubectl get ingress -n h5-dev
```

### 直接使用 YAML 文件部署

```bash
# 替换模板变量后部署
envsubst < deploy/kustomize/base/deployment.yml | kubectl apply -f -
envsubst < deploy/kustomize/base/ingress.yml | kubectl apply -f -
```

## 环境配置

### 域名配置

- **生产环境**: `h5.xiaoxingcloud.com`
- **开发环境**: `h5-dev.xiaoxingcloud.com`

### 部署变量

| 变量名     | 描述                | 生产环境             | 开发环境                 |
| ---------- | ------------------- | -------------------- | ------------------------ |
| NAMESPACE  | Kubernetes 命名空间 | h5-prod              | h5-dev                   |
| BUILD_TAG  | 镜像标签            | v1.0.0               | dev-latest               |
| REPLICAS   | 副本数量            | 2                    | 1                        |
| DOMAIN     | 域名                | h5.xiaoxingcloud.com | h5-dev.xiaoxingcloud.com |
| TLS_SECRET | TLS 证书密钥        | h5-prod-tls          | h5-dev-tls               |

### 前端环境变量

前端环境变量在**构建时**注入，不是运行时：

- 使用 `VITE_*` 前缀的环境变量
- 在CI/CD流程中通过Infisical注入到`.env`文件
- 构建时通过Vite读取并打包到静态文件中

## 镜像构建

### 本地构建

```bash
# 构建生产镜像
docker build -f deploy/Dockerfile -t xiaoxing-h5:latest .

# 推送到镜像仓库
docker tag xiaoxing-h5:latest harbor.xiaoxingcloud.com/app/xiaoxing-h5:v1.0.0
docker push harbor.xiaoxingcloud.com/app/xiaoxing-h5:v1.0.0
```

### CI/CD 构建

在 CI/CD 流水线中使用以下命令：

```bash
# 1. 准备环境变量文件（通过Infisical或其他方式）
infisical export --env="${ENV}" --projectId="${INFISICAL_PROJECT_ID}" --path="/${CI_PROJECT_NAME}" > .env

# 2. 构建镜像（环境变量会在构建时注入）
docker build -f deploy/Dockerfile -t harbor.xiaoxingcloud.com/app/xiaoxing-h5:${BUILD_TAG} .

# 3. 推送镜像
docker push harbor.xiaoxingcloud.com/app/xiaoxing-h5:${BUILD_TAG}

# 4. 清理环境变量文件
rm .env
```

### 本地开发构建

如果需要本地构建不同环境的镜像：

```bash
# 1. 准备环境变量文件
cp .env.example .env
# 编辑 .env 文件，添加 VITE_* 环境变量

# 2. 构建镜像
docker build -f deploy/Dockerfile -t xiaoxing-h5:local .
```

## 健康检查

应用提供以下健康检查端点：

- **Readiness Probe**: `GET /` - 检查应用是否准备好接收流量
- **Liveness Probe**: `GET /` - 检查应用是否正常运行

## 资源配置

### 生产环境

- **CPU**: 请求 200m，限制 1000m
- **内存**: 请求 256Mi，限制 1Gi
- **副本数**: 2

### 开发环境

- **CPU**: 请求 100m，限制 500m
- **内存**: 请求 128Mi，限制 512Mi
- **副本数**: 1

## 故障排查

### 查看 Pod 日志

```bash
# 生产环境
kubectl logs -f deployment/xiaoxing-h5 -n h5-prod

# 开发环境
kubectl logs -f deployment/xiaoxing-h5 -n h5-dev
```

### 查看 Pod 状态

```bash
kubectl describe pod <pod-name> -n <namespace>
```

### 查看 Ingress 状态

```bash
kubectl describe ingress xiaoxing-h5-ingress -n <namespace>
```
