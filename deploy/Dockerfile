FROM harbor.xiaoxingcloud.com/foundation/node:24-slim AS builder
# local or docker hub
# FROM node:24-slim AS runtime

ARG mode=prod
WORKDIR /app

# Configure apt mirror for faster downloads and install packages
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources || \
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list
RUN apt-get update
RUN apt-get install -y ca-certificates

COPY package*.json pnpm-lock.yaml ./
RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g pnpm
RUN pnpm install

# Copy source code and environment files
COPY . .

# Build the application (environment variables will be read from .env file if present)
RUN pnpm run build:${mode}


FROM harbor.xiaoxingcloud.com/foundation/nginx:latest AS runtime
# local or docker hub
# FROM nginx:latest

COPY --from=builder /app/dist /usr/share/nginx/html
COPY --from=builder /app/deploy/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]