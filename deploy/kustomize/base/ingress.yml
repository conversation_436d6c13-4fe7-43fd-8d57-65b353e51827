apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: xiaoxing-h5-ingress
  labels:
    app: xiaoxing-h5
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/cors-allow-headers: 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization'
    nginx.ingress.kubernetes.io/cors-allow-methods: 'GET, POST, PUT, DELETE, OPTIONS'
    nginx.ingress.kubernetes.io/cors-allow-origin: '*'
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/force-ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: 50m
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '600'
    # Enable gzip compression
    nginx.ingress.kubernetes.io/enable-gzip: 'true'
spec:
  tls:
    - hosts:
        - h5.xiaoxingcloud.com
      secretName: xiaoxingcloud.com
  rules:
    - host: h5.xiaoxingcloud.com
      http:
        paths:
          - path: /
            backend:
              serviceName: xiaoxing-h5-svc
              servicePort: 80
