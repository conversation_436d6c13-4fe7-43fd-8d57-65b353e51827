apiVersion: apps/v1
kind: Deployment
metadata:
  name: xiaoxing-h5
  labels:
    app: xiaoxing-h5
spec:
  replicas: 1
  selector:
    matchLabels:
      app: xiaoxing-h5
  template:
    metadata:
      labels:
        app: xiaoxing-h5
    spec:
      containers:
        - name: xiaoxing-h5
          image: harbor.xiaoxingcloud.com/app/xiaoxing-h5:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 80
              name: http
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 15
            timeoutSeconds: 3
            failureThreshold: 3
      imagePullSecrets:
        - name: registry-pull-secret

---
apiVersion: v1
kind: Service
metadata:
  name: xiaoxing-h5-svc
  labels:
    app: xiaoxing-h5
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
      name: http
  selector:
    app: xiaoxing-h5
