server {
  listen 80;
  server_name localhost;

  # Static assets with caching
  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    root   /usr/share/nginx/html;
    expires 1y;
    add_header Cache-Control "public, immutable";
  }

  # HTML files without caching
  location / {
    add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
    expires off;
    proxy_no_cache 1;

    root   /usr/share/nginx/html;
    index  index.html;
    try_files $uri $uri/ /index.html;
  }

  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}