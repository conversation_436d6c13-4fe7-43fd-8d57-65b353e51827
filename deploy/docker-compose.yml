version: '3.8'

services:
  web:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    image: xiaoxing-h5:${TAG:-latest}
    container_name: xiaoxing-h5-${ENV:-prod}
    ports:
      - "${PORT:-80}:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - xiaoxing-h5

networks:
  xiaoxing-h5:
    driver: bridge