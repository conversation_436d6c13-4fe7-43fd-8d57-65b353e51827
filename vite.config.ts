import process from 'node:process';
import { fileURLToPath, URL } from 'node:url';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import vue from '@vitejs/plugin-vue';
import UnoCSS from 'unocss/vite';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const releaseName = `h5-${env.APP_ENV_MODE || mode}-${env.APP_VERSION}`;

  return {
    build: {
      sourcemap: true,
    },
    plugins: [
      vue(),
      UnoCSS(),
      sentryVitePlugin({
        debug: true,
        authToken: env.SENTRY_AUTH_TOKEN,
        org: 'sentry',
        project: 'h5',
        url: 'https://sentry.xiaoxingcloud.com',
        telemetry: false,
        release: {
          name: releaseName,
          uploadLegacySourcemaps: [
            {
              urlPrefix: '~/assets/',
              ignore: ['./node_modules'],
              paths: ['dist/assets'],
            },
          ],
        },
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    define: {
      __DEV__: ['dev', 'test', 'development', 'testing', 'local'].includes(mode),
      __APP_ENV_MODE__: JSON.stringify(env.APP_ENV_MODE || mode),
      __APP_RELEASE_VERSION__: JSON.stringify(releaseName),
    },
    server: {
      port: 5174,
      proxy: {
        '/api': {
          target: 'https://ai-dev.xiaoxingcloud.com',
          changeOrigin: true,
        },
        '/customIP': {
          target: 'http://************:8000',
          rewrite: (path) => {
            return path.replace('/customIP', '');
          },
        },
      },
    },
  };
});
