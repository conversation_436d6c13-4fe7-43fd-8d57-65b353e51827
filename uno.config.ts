import type { PresetUnoTheme } from 'unocss';
import { defineConfig, presetAttributify, presetWind3, transformerDirectives, transformerVariantGroup } from 'unocss';

export default defineConfig({
  presets: [
    presetAttributify(),
    presetWind3(),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  extendTheme: (theme: PresetUnoTheme) => {
    return {
      ...theme,
      breakpoints: {
        ...theme.breakpoints,
        DEFAULT: '1920px',
      },
    };
  },
});
