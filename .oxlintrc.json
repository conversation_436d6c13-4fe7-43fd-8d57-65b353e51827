{"$schema": "./node_modules/oxlint/configuration_schema.json", "plugins": ["node", "jsdoc", "import", "unicorn"], "categories": {"correctness": "off"}, "env": {"builtin": true, "browser": true, "commonjs": true, "es2024": true, "node": true, "shared-node-browser": true}, "ignorePatterns": ["**/.env", "**/logs", "**/*.log", "**/npm-debug.log*", "**/yarn-debug.log*", "**/yarn-error.log*", "**/lerna-debug.log*", "**/.pnpm-debug.log*", "**/report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json", "**/pids", "**/*.pid", "**/*.seed", "**/*.pid.lock", "**/lib-cov", "**/coverage", "**/*.lcov", "**/.nyc_output", "**/.grunt", "**/bower_components", "**/.lock-wscript", "build/Release", "**/node_modules/", "**/jspm_packages/", "**/web_modules/", "**/*.tsbuildinfo", "**/.npm", "**/.eslintcache", "**/.<PERSON><PERSON><PERSON><PERSON>", "**/.rpt2_cache/", "**/.rts2_cache_cjs/", "**/.rts2_cache_es/", "**/.rts2_cache_umd/", "**/.node_repl_history", "**/*.tgz", "**/.yarn-integrity", "**/.env.development.local", "**/.env.test.local", "**/.env.production.local", "**/.env.local", "**/.cache", "**/.parcel-cache", "**/.next", "**/out", "**/.nuxt", "**/dist", "**/.cache/", ".vuepress/dist", "**/.temp", "**/.docusaurus", "**/.serverless/", "**/.fusebox/", "**/.dynamodb/", "**/.tern-port", "**/.vscode-test", ".yarn/cache", ".yarn/unplugged", ".yarn/build-state.yml", ".yarn/install-state.gz", "**/.pnp.*", "**/.webpack/", "**/.svelte-kit", ".vscode/*", "!.vscode/settings.json", "!.vscode/tasks.json", "!.vscode/launch.json", "!.vscode/extensions.json", "!.vscode/*.code-snippets", "**/.history/", "**/*.vsix", "**/.history", "**/.ionide", "docs/_book", "**/test/", "!deploy/**/test", "**/dist/", "**/npm-debug.log", "**/yarn-error.log", "**/node_modules", "**/package-lock.json", "**/yarn.lock", "**/pnpm-lock.yaml", "**/bun.lockb", "**/output", "**/temp", "**/tmp", "**/.tmp", "**/.vitepress/cache", "**/.vercel", "**/.changeset", "**/.idea", "**/.output", "**/.vite-inspect", "**/.yarn", "**/vite.config.*.timestamp-*", "**/CHANGELOG*.md", "**/*.min.*", "**/LICENSE*", "**/__snapshots__", "**/auto-import?(s).d.ts", "**/components.d.ts", "node_modules/", "node_modules/**/", ".vscode/", ".vscode/**/", "public/", "public/**/", "dist/", "dist/**/", "tsconfig.json", "**/tsconfig.json/**", "assets/", "**/assets/**"], "globals": {"computed": "readonly", "defineEmits": "readonly", "defineExpose": "readonly", "defineProps": "readonly", "onMounted": "readonly", "onUnmounted": "readonly", "reactive": "readonly", "ref": "readonly", "shallowReactive": "readonly", "shallowRef": "readonly", "toRef": "readonly", "toRefs": "readonly", "watch": "readonly", "watchEffect": "readonly"}, "rules": {"array-callback-return": "error", "block-scoped-var": "error", "default-case-last": "error", "eqeqeq": ["error", "smart"], "new-cap": ["error", {"capIsNew": false, "newIsCap": true, "properties": true}], "no-alert": "error", "no-array-constructor": "error", "no-async-promise-executor": "error", "no-caller": "error", "no-case-declarations": "error", "no-class-assign": "error", "no-compare-neg-zero": "error", "no-cond-assign": ["error", "always"], "no-console": "off", "no-const-assign": "error", "no-control-regex": "error", "no-debugger": "error", "no-delete-var": "error", "no-dupe-class-members": "error", "no-dupe-keys": "error", "no-duplicate-case": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-empty-character-class": "off", "no-empty-pattern": "error", "no-eval": "error", "no-ex-assign": "error", "no-extend-native": "error", "no-extra-bind": "error", "no-extra-boolean-cast": "error", "no-fallthrough": "error", "no-func-assign": "error", "no-global-assign": "error", "no-import-assign": "error", "no-invalid-regexp": "off", "no-irregular-whitespace": "error", "no-iterator": "error", "no-labels": ["error", {"allowLoop": false, "allowSwitch": false}], "no-lone-blocks": "error", "no-loss-of-precision": "error", "no-multi-str": "error", "no-new": "error", "no-new-func": "error", "no-new-native-nonconstructor": "error", "no-new-wrappers": "error", "no-obj-calls": "error", "no-proto": "error", "no-prototype-builtins": "error", "no-redeclare": ["error", {"builtinGlobals": false}], "no-regex-spaces": "error", "no-restricted-globals": ["error", {"message": "Use `globalThis` instead.", "name": "global"}, {"message": "Use `globalThis` instead.", "name": "self"}], "no-self-assign": ["error", {"props": true}], "no-self-compare": "error", "no-shadow-restricted-names": "error", "no-sparse-arrays": "error", "no-template-curly-in-string": "error", "no-this-before-super": "error", "no-throw-literal": "error", "no-unexpected-multiline": "error", "no-unneeded-ternary": ["error", {"defaultAssignment": false}], "no-unsafe-finally": "error", "no-unsafe-negation": "error", "no-unused-expressions": ["error", {"allowShortCircuit": true, "allowTaggedTemplates": true, "allowTernary": true}], "no-unused-vars": ["error", {"args": "none", "caughtErrors": "none", "ignoreRestSiblings": true, "vars": "all"}], "no-useless-backreference": "off", "no-useless-call": "error", "no-useless-catch": "error", "no-useless-constructor": "error", "no-useless-rename": "error", "no-var": "error", "no-with": "error", "prefer-exponentiation-operator": "error", "prefer-promise-reject-errors": "error", "prefer-rest-params": "error", "prefer-spread": "error", "symbol-description": "error", "unicode-bom": ["error", "never"], "use-isnan": ["error", {"enforceForIndexOf": true, "enforceForSwitchCase": true}], "valid-typeof": ["error", {"requireStringLiterals": true}], "vars-on-top": "error", "yoda": ["error", "never"], "node/no-exports-assign": "error", "node/no-new-require": "error", "jsdoc/check-access": "warn", "jsdoc/check-property-names": "warn", "jsdoc/empty-tags": "warn", "jsdoc/implements-on-classes": "warn", "jsdoc/no-defaults": "warn", "jsdoc/require-param-name": "warn", "jsdoc/require-property": "warn", "jsdoc/require-property-description": "warn", "jsdoc/require-property-name": "warn", "jsdoc/require-returns-description": "warn", "import/consistent-type-specifier-style": ["error", "top-level"], "import/first": "error", "import/no-duplicates": "error", "import/no-mutable-exports": "error", "import/no-named-default": "error", "unicorn/consistent-empty-array-spread": "error", "unicorn/error-message": "error", "unicorn/escape-case": "error", "unicorn/new-for-builtins": "error", "unicorn/no-instanceof-builtins": "error", "unicorn/no-new-array": "error", "unicorn/no-new-buffer": "error", "unicorn/number-literal-case": "error", "unicorn/prefer-dom-node-text-content": "error", "unicorn/prefer-includes": "error", "unicorn/prefer-node-protocol": "error", "unicorn/prefer-number-properties": "error", "unicorn/prefer-string-starts-ends-with": "error", "unicorn/prefer-type-error": "error", "unicorn/throw-new-error": "error"}, "overrides": [{"files": ["**/*.?([cm])ts", "**/*.?([cm])tsx", "**/*.vue"], "rules": {"no-class-assign": "off", "no-const-assign": "off", "no-dupe-class-members": "off", "no-dupe-keys": "off", "no-func-assign": "off", "no-import-assign": "off", "no-new-native-nonconstructor": "off", "no-obj-calls": "off", "no-redeclare": "off", "no-setter-return": "off", "no-this-before-super": "off", "no-unsafe-negation": "off", "no-with": "off", "no-array-constructor": "off", "no-unused-expressions": "off", "no-unused-vars": "off", "no-useless-constructor": "off"}}, {"files": ["**/__tests__/**/*.?([cm])[jt]s?(x)", "**/*.spec.?([cm])[jt]s?(x)", "**/*.test.?([cm])[jt]s?(x)", "**/*.bench.?([cm])[jt]s?(x)", "**/*.benchmark.?([cm])[jt]s?(x)"], "rules": {"no-unused-expressions": "off"}}, {"files": ["**/*.md/**/*.?([cm])[jt]s?(x)", "**/*.md/**/*.vue"], "rules": {"no-alert": "off", "no-labels": "off", "no-lone-blocks": "off", "no-unused-expressions": "off", "no-unused-labels": "off", "no-unused-vars": "off", "unicode-bom": "off"}}]}