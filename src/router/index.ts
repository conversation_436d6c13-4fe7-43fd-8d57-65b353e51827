import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/math/search-question',
      name: 'searchQuestion',
      component: () => import('@/views/math/search-question.vue'),
    },
    {
      path: '/math/ai-explain',
      name: 'AiExplain',
      component: () => import('@/views/math/ai-explain.vue'),
    },
  ],
});
export default router;
