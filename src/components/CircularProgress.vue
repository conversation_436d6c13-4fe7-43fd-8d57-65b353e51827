<template>
  <!-- 圆形进度条倒计时 -->
  <div class="circular-progress">
    <svg>
      <circle class="bg-circle" cx="36" cy="36" r="30" />
      <circle class="progress-circle" cx="36" cy="36" r="30" />
    </svg>
    <div class="circular-number">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
defineSlots<{
  default: string | number;
}>();
</script>

<style lang="scss" scoped>
/* 圆形进度条倒计时 */
.circular-progress {
  position: relative;
  display: inline;
  width: 72px;
  height: 72px;
}

.circular-progress svg {
  transform: rotate(-90deg);
  width: 100%;
  height: 100%;
}

.circular-progress circle {
  fill: none;
  stroke-width: 8;
}

.bg-circle {
  stroke: #c6e5ff;
}

.progress-circle {
  stroke: #2780fe;
  stroke-linecap: round;
  stroke-dasharray: 226;
  stroke-dashoffset: 226;
  animation: countdown-circle 5s linear infinite;
}

@keyframes countdown-circle {
  from {
    stroke-dashoffset: 226;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.circular-number {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 40px;
  font-weight: 600;
  color: #2780fe;
}
</style>
