<template>
  <div class="wave-loader">
    <template v-for="(color, index) of colors" :key="index">
      <span v-if="index < barNum" :style="`--i:${index};--c:${color};`" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

withDefaults(defineProps<{
  barNum?: number;
}>(), {
  barNum: 10,
});

const colors = ref([
  '#B640FF',
  '#9a4efe',
  '#9054ff',
  '#855bff',
  '#7960ff',
  '#6e66ff',
  '#626dff',
  '#5773ff',
  '#4d78ff',
  '#427eff',
]);
</script>

<style scoped>
.wave-loader {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 40px;
  background: transparent;
  /* 背景色 */
  padding: 10px;
  border-radius: 8px;
}

.wave-loader span {
  width: 6px;
  height: 10px;
  background-color: var(--c);
  /* 紫到蓝渐变 */
  border-radius: 3px;
  animation: wave 1s infinite ease-in-out;
  animation-delay: calc(var(--i) * 0.1s);
}

@keyframes wave {
  0%,
  100% {
    height: 25%;
  }

  50% {
    height: 100%;
  }
}
</style>
