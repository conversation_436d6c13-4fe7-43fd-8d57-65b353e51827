<template>
  <div
    v-for="(item, idx) in tabs" :key="item.name"
    class="h-10 w-32 flex items-center justify-center text-center" :class="[
      activeTab === idx
        ? 'text-white font-bold bg-blue-50 border border-blue-600 tab-bg'
        : 'text-gray-500 border border-gray-200',
    ]"
    @click="() => {
      if (item.disabled && item.disabled()) return
      activeTab = idx;
      emit('handleTab', idx);
    }"
  >
    {{ item.name }}
  </div>
</template>

<script setup lang="ts">
defineProps<{
  tabs: {
    name: string;
    disabled?: () => boolean;
  }[];
}>();
const emit = defineEmits<{
  (e: 'handleTab', idx: number): void;
}>();
const activeTab = defineModel<number>('activeTab', { default: 0 });
</script>

<style lang="scss" scoped>
.tab-bg {
  background-image: url('@/assets/tab-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>