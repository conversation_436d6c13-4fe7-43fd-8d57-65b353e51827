<template>
  <div class="h-screen">
    <div class="w-full flex flex-col">
      <div class="fixed h-16 w-full border-b-2 border-gray-200 bg-white">
        <div class="w-full flex items-center justify-center gap-4 py-4">
          <Tabs :tabs="tabs" @handle-tab="handleScrollToSection" />
        </div>
      </div>
      <div class="container mx-auto box-border px-4 py-6 pt-18">
        <div
          v-for="(item, idx) in tabs" :id="`section-${idx}`" :key="item.name"
          class="mb-10 rounded-lg bg-white p-6 shadow-md"
        >
          <h2 class="mb-4 text-xl text-blue-600 font-bold">
            {{ item.name }}
          </h2>
          <div class="content" v-html="item.content" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue';
import api from '@/api';
import Tabs from '@/components/tabs.vue';

const tabs = ref([
  {
    name: '题目',
    content: '',
  },
  {
    name: '答案',
    content: '',
  },
  {
    name: '答案解析',
    content: '',
  },
]);
function handleScrollToSection(index: number) {
  const element = document.getElementById(`section-${index}`);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}

async function androidBridgeFetchData(params: {
  token: string;
  ocr_record_id: string;
  question?: string;
  image_base64?: string;
  question_index: number;
}) {
  const { token, ocr_record_id, question: q, image_base64, question_index } = params;
  const { data } = await api.searchQuestion({
    ocr_record_id,
    question: q,
    image_base64,
    question_index,
  }, {
    Authorization: token,
  });
  const { answer, explanation, question } = data.questions[0];
  showResult({
    question,
    answer,
    explanation,
  });
  window.AndroidInterface.onReceivedOCRData(JSON.stringify(data)); // 回传参数给Android
}

// 搜题详情
async function androidBridgeFetchDetail(params: {
  question: string;
  ocr_record_id: string;
  question_index: number;
  token: string;
}) {
  const { ocr_record_id, question_index, token } = params;
  const { data } = await api.getQuestionDetail({ ocr_record_id, question_index }, {
    Authorization: token,
  });

  if (!data.search_result) {
    return androidBridgeFetchData({
      token,
      ocr_record_id,
      question: params.question,
      question_index,
    });
  }
  const { question, explanation, answer } = data.search_result[0];
  showResult({
    question,
    answer,
    explanation,
  });
  window.AndroidInterface.onReceivedOCRData(JSON.stringify(data)); // 回传参数给Android
}

async function showResult(params: {
  question: string;
  answer: string;
  explanation: string;
}) {
  const { question, answer, explanation } = params;
  tabs.value[0].content = question;
  tabs.value[1].content = answer;
  tabs.value[2].content = explanation;
  await window.MathJax.startup.promise;
  // 使用nextTick
  nextTick(async () => {
    await window.MathJax?.typesetPromise();
  });
}

window.androidBridgeFetchData = androidBridgeFetchData;
window.androidBridgeFetchDetail = androidBridgeFetchDetail;
</script>

<style>
@import url('https://static.tiku.100tal.com/xes_souti/assets/sdk/h5/common/static/default.css');

:not(.MathJax *) {
  /* 公式缩放大小 */
  font-size-adjust: 0.5;
}

/* 强制行内公式 */
mjx-container[jax='CHTML'][display='true'],
mjx-container[jax='SVG'][display='true'] {
  display: inline-block !important;
  margin: 0 !important;
}
</style>
