import * as Sentry from '@sentry/vue';
import eruda from 'eruda';
import katex from 'katex';
import { config } from 'md-editor-v3';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import 'katex/dist/katex.min.css';
import 'virtual:uno.css';

if (__DEV__) {
  eruda.init();
}

config({
  editorExtensions: {
    katex: {
      instance: katex,
    },
  },
});

const app = createApp(App);

Sentry.init({
  app,
  release: __APP_RELEASE_VERSION__,
  environment: __APP_ENV_MODE__,
  dsn: 'https://<EMAIL>/21',
  integrations: [
    Sentry.browserTracingIntegration({ router }),
    Sentry.replayIntegration({
      networkDetailAllowUrls: [
        window.location.origin,
        /^(https?:\/\/)?ai(?:-(dev|test))?\.xiaoxingcloud\.com(:\d+)?$/,
      ],
    }),
  ],
  // Performance Monitoring
  tracesSampleRate: 1.0, //  Capture 100% of the transactions
  // Session Replay
  replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

app.use(router);
app.mount('#app');
