import type { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import type { ApiResponse } from './modules/base.dto';
import axios from 'axios';

const axiosInstance = axios.create({});
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    config.url = `${import.meta.env.VITE_BASE_URL}${config.url}`; // 重写
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse<ApiResponse, any> => {
    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  },
);
export default axiosInstance;
