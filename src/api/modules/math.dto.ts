import { z } from 'zod';

/**
 * 类型Schema
 */

export const SearchQuestionParamsSchema = z.object({
  ocr_record_id: z.string().min(1, '记录ID不能为空'),
  question: z.string().optional(),
  image_base64: z.string().optional(),
  question_index: z.number().int().min(0, '问题索引必须为非负整数'),
});

export const SearchQuestionSchema = z.object({
  id: z.string().describe('问题分布式ID'),
  question: z.string(),
  answer: z.string(),
  explanation: z.string(),
  similarity: z.number().describe('相似度，范围 0-1，值越大越相似'),
  metadata: z.object({
    js: z.array(z.url()).describe('渲染使用的JS链接'),
    css: z.array(z.url()).describe('渲染使用的CSS链接'),
  }),
});

export const SearchQuestionResponseSchema = z.object({
  questions: z.array(SearchQuestionSchema),
});

// 获取问题详情请求参数类型
export const GetQuestionDetailParamsSchema = z.object({
  ocr_record_id: z.string().min(1, '记录ID不能为空'),
  question_index: z.number().int().min(0, '问题索引必须为非负整数'),
});

export const GetQeustionDetailResponseSchema = z.object({
  session_id: z.string().describe('会话ID'),
  record_id: z.string().describe('记录ID'),
  question: z.string().describe('问题'),
  question_index: z.number().int().min(0, '问题索引必须为非负整数').describe('问题索引'),
  created_at: z.string().describe('创建时间'),
  search_result: z.array(SearchQuestionSchema).describe('搜索结果'),
  solution_idea: z.array(z.string()).describe('解题思路'),
  knowledge_points: z.array(z.string()).describe('知识点'),
  solution_steps: z.array(z.string()).describe('解题步骤'),
  chat_messages: z.record(z.string(), z.string()).describe('聊天记录'),
});

/**
 * 类型导出
 */

// 搜题
export type SearchQuestionParams = z.infer<typeof SearchQuestionParamsSchema>;
export type SearchQuestionResponse = z.infer<typeof SearchQuestionResponseSchema>;

// 问题详情
export type GetQuestionDetailParams = z.infer<typeof GetQuestionDetailParamsSchema>;
export type GetQeustionDetailResponse = z.infer<typeof GetQeustionDetailResponseSchema>;
