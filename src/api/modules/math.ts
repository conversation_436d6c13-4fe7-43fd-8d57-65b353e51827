import type { HTTPHeaders } from './base.dto';
import type { GetQeustionDetailResponse, GetQuestionDetailParams, SearchQuestionParams, SearchQuestionResponse } from './math.dto';
import request from '../http';

import {
  GetQuestionDetailParamsSchema,
  SearchQuestionParamsSchema,
} from './math.dto';

async function searchQuestion(
  params: SearchQuestionParams,
  headers: HTTPHeaders,
) {
  // 验证参数
  const validatedParams = SearchQuestionParamsSchema.parse(params);
  return request.post<SearchQuestionResponse>('/api/v1/math/search-question', { ...validatedParams }, { headers });
}

async function getQuestionDetail(
  params: GetQuestionDetailParams,
  headers: HTTPHeaders,
) {
  // 验证参数
  const validatedParams = GetQuestionDetailParamsSchema.parse(params);

  return request.get<GetQeustionDetailResponse>('/api/v1/math/question/detail', {
    params: validatedParams,
    headers,
  });
}

export { getQuestionDetail, searchQuestion };
