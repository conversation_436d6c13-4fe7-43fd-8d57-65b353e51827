{"name": "h5", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --host", "build": "vue-tsc -b && vite build", "build:prod": "vite build", "build:dev": "vite build --mode development", "preview": "vite preview", "lint": "ox<PERSON>"}, "dependencies": {"@unocss/reset": "^66.3.3", "@vueuse/components": "^13.5.0", "@vueuse/core": "^13.5.0", "axios": "^1.10.0", "eruda": "^3.4.3", "fetch-sse": "^1.1.2", "katex": "^0.16.22", "md-editor-v3": "^5.7.1", "vconsole": "^3.15.1", "vue": "^3.5.17", "vue-router": "4", "zod": "^4.0.5"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "@sentry/vite-plugin": "^4.0.2", "@sentry/vue": "^10.3.0", "@types/node": "^24.0.13", "@unocss/eslint-plugin": "^66.3.2", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.30.1", "eslint-plugin-format": "^1.0.1", "eslint-plugin-oxlint": "^1.6.0", "oxlint": "^1.6.0", "sass": "^1.89.2", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "^7.0.3", "vue-tsc": "^2.2.12"}, "lint-staged": {"**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,astro,svelte}": "ox<PERSON>"}}